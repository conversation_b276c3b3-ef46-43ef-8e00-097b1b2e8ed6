<?xml version="1.0"?>
<package>
    <name>gv_tools</name>
    <version>0.0.1</version>
    <description>Ground-vision toolkit.</description>

    <maintainer email="<EMAIL>"><PERSON><PERSON><PERSON></maintainer>

    <license>GPLv3</license>

    <buildtool_depend>catkin</buildtool_depend>
    <build_depend>roscpp</build_depend>
    <build_depend>rospy</build_depend>
    <build_depend>std_msgs</build_depend>
    <build_depend>cv_bridge</build_depend>
    <build_depend>image_transport</build_depend>
    <build_depend>tf</build_depend>
    <build_depend>sensor_msgs</build_depend>
    <run_depend>roscpp</run_depend>
    <run_depend>rospy</run_depend>
    <run_depend>std_msgs</run_depend>
    <run_depend>cv_bridge</run_depend>
    <run_depend>image_transport</run_depend>
    <run_depend>tf</run_depend>
    <run_depend>sensor_msgs</run_depend>
    <export>
    </export>
</package>