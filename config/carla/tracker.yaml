%YAML:1.0
---

imu_topic: "/imu0"
image0_topic: "/cam0/image_raw"
image1_topic: "/cam1/image_raw"

cam0_calib: "cam0_pinhole.yaml"
cam1_calib: "cam1_pinhole.yaml"

IPM_WIDTH: 400
IPM_HEIGHT: 1000
IPM_RESO: 0.015

priori_alpha: 0.0
priori_theta: 0.0
priori_H: 1.84

body_T_cam0: !!opencv-matrix
   rows: 4
   cols: 4
   dt: d
   data: [ 1, 0, 0, 0,
           0, 0, 1, 0,
           0, -1, 0, 0,
           0.0000000000, 0.0000000000, 0.0000000000, 1.0000000000]


body_T_cam1: !!opencv-matrix
   rows: 4
   cols: 4
   dt: d
   data: [ 1, 0, 0, 0,
           0, 0, 1, 0,
           0, -1, 0, 0,
           0.0000000000, 0.0000000000, 0.0000000000, 1.0000000000]


enable_pitch_comp: 1
pitch_comp_windowsize: 4.0

feature_mode: 1 # 0: min dist-based; 1: grid-based

# min dist-based
min_dist_ground: 30
max_cnt_ground: 40

# grid-based
grid_row: 8
grid_col: 4
grid_min_feature_num: 2
grid_max_feature_num: 3
